package models

type Courses struct {
	ID                uint   `json:"id" gorm:"primaryKey"`
	CourseName        string `json:"course_name" gorm:"unique" binding:"required"`
	CoursePicture     string `json:"course_picture"`
	CourseDescription string `json:"course_description"`
	CourseInstruction string `json:"course_instruction"`
	CourseDifficulty  string `json:"course_difficulty"`
	CourseDuration    string `json:"course_duration"`
	CourseStatus      string `json:"course_status"`
	CourseCertificate bool   `json:"course_certificate"`
	Slug              string `json:"user_slug"`

	LecturerID uint     `json:"user_id"`
	Lecture    UserInfo `json:"user" gorm:"foreignKey:LecturerID"`
}
