package main

import (
	"med-api/db"
	"med-api/routes"
	"med-api/utils"

	"github.com/canvas-tech-horizon/notelink"
)

func main() {
	jwtSecret := utils.LoadEnv()
	db.InitDB()

	config := notelink.Config{
		Title:       "e-mad API Documentation",
		Description: "A e-mad API with documentation",
		Version:     "1.0.0",
		Host:        "localhost:8000",
	}

	api := notelink.NewApiNote(&config, jwtSecret)

	routes.SetupRoutes(api)

	// backend_routes.LoginRoutesApi(api)       //SignUP's API docs routes
	// backend_routes.UserApiRoutes(api)        //User's API docs routes
	// backend_routes.LessonsApiRoutes(api)     //Lesson's API docs routes
	// backend_routes.CoursesRoutesApi(api)     //Course's API docs routes
	// backend_routes.CertificateRoutesApi(api) //Certificate's API docs routes

	// backend_routes.ExamsRoutesApi(api)       //Exam's API docs routes
	// backend_routes.QuestionsRoutesApi(api)   //Question's API docs routes
	// backend_routes.QuizsRoutesApi(api)       //Quiz's API docs routes
	// backend_routes.TypesRoutesApi(api)       //Type's API docs routes
	// backend_routes.QuizChoicesRoutesApi(api) //Quiz Choice's API docs routes
	// backend_routes.RecordRoutesApi(api)      //Exam record's API docs routes
	// backend_routes.ProgressRoutesApi(api)    //Content Progress's API docs routes
	// backend_routes.ContentLessonRoutesApi(api) //Content Lesson's API docs routes

	if err := api.Listen(); err != nil {
		panic(err)
	}
}
