package controllers

import (
	"errors"
	"med-api/db"
	"med-api/models"
	models_api "med-api/models-api"
	"med-api/utils"
	"time"

	"gorm.io/gorm"

	"github.com/gofiber/fiber/v2"
)

func CreateUsers(c *fiber.Ctx) error {
	var user models_api.CreateUser

	if err := c.BodyParser(&user); err != nil {
		return c.Status(fiber.StatusBadRequest).JSON(fiber.Map{"error": err.Error()})
	}
	hashedPassword, err := utils.HashPassword(user.Password)
	if err != nil {
		return c.Status(fiber.StatusInternalServerError).JSON(fiber.Map{
			"error": "Failed to hash password",
		})
	}

	user.Password = hashedPassword

	CreateUser := models.UserInfo{
		Username:  user.Username,
		Password:  user.Password,
		FirstName: user.FirstName,
		LastName:  user.LastName,
		Email:     user.Email,
		Position:  user.Position,
		LoginDate: time.Now().Format("2006-01-02 15:04:05"),
		RoleID:    1,
		Slug:      utils.GenerateUUIDSlug(),
	}

	if err := db.DB.Where("username = ?", user.Username).First(&CreateUser).Error; err == nil {
		return c.Status(fiber.StatusBadRequest).JSON(fiber.Map{"error": "Username already exists"})
	}

	if err := db.DB.Create(&CreateUser).Error; err != nil {
		return c.Status(fiber.StatusInternalServerError).JSON(fiber.Map{
			"error": "Failed to create user",
		})
	}

	return c.Status(fiber.StatusCreated).JSON(fiber.Map{"message": "Created successful"})
}

func GetUsers(c *fiber.Ctx) error {
	var users []models.UserInfo
	var allUsersResponse []models_api.AllUsersRespond

	if err := db.DB.Preload("Role").Find(&users).Error; err != nil {
		return c.Status(fiber.StatusBadRequest).JSON(fiber.Map{"error": err.Error()})
	}
	for _, user := range users {
		allUsersResponse = append(allUsersResponse, models_api.AllUsersRespond{
			Picture:   user.Picture,
			FirstName: user.FirstName,
			LastName:  user.LastName,
			Email:     user.Email,
			Position:  user.Position,
			LoginDate: user.LoginDate,
			Status:    user.Status,
			Role:      user.Role.Name,
		})
	}

	return c.Status(fiber.StatusOK).JSON(allUsersResponse)
}

func GetUserID(c *fiber.Ctx) error {
	var user models.UserInfo

	slug := c.Params("slug")
	if err := db.DB.Preload("Role").Where("slug = ?", slug).First(&user).Error; err != nil {
		return c.Status(fiber.StatusNotFound).JSON(fiber.Map{"error": "Item not found"})
	}

	userRole := c.Locals("role")
	if userRole == "admin" {
		oneUser := models_api.UserRespond{
			Picture:   user.Picture,
			Password:  user.Password,
			FirstName: user.FirstName,
			LastName:  user.LastName,
			Email:     user.Email,
			Role:      &user.Role.Name,
			Status:    &user.Status,
			LoginDate: &user.LoginDate,
		}
		return c.Status(fiber.StatusOK).JSON(oneUser)
	}

	oneUser := models_api.UserRespond{
		Picture:   user.Picture,
		Password:  user.Password,
		FirstName: user.FirstName,
		LastName:  user.LastName,
		Email:     user.Email,
	}
	return c.Status(fiber.StatusOK).JSON(oneUser)
}

func UpdateUser(c *fiber.Ctx) error {
	var user models_api.UserRequest

	slug := c.Params("slug")
	if err := c.BodyParser(&user); err != nil {
		return c.Status(fiber.StatusBadRequest).JSON(fiber.Map{"error": err.Error()})
	}

	hashedPassword, err := utils.HashPassword(user.Password)
	if err != nil {
		return c.Status(fiber.StatusInternalServerError).JSON(fiber.Map{
			"error": "Failed to hash password",
		})
	}

	user.Password = hashedPassword

	updated_user := models.UserInfo{
		Picture:   user.Picture,
		Password:  user.Password,
		FirstName: user.FirstName,
		LastName:  user.LastName,
		Email:     user.Email,
		Position:  user.Position,
		Role:      models.Role{Name: user.Role},
		Status:    user.Status,
		LoginDate: user.LoginDate,
	}

	if err := db.DB.Model(&models.UserInfo{}).Where("slug = ?", slug).Updates(updated_user).Error; err != nil {
		return c.Status(fiber.StatusInternalServerError).JSON(fiber.Map{"error": "Failed to update user"})
	}

	userRole := c.Locals("role")
	if userRole == "admin" {
		oneUser := models_api.UserRespond{
			Picture:   updated_user.Picture,
			Password:  updated_user.Password,
			FirstName: updated_user.FirstName,
			LastName:  updated_user.LastName,
			Email:     updated_user.Email,
			Position:  updated_user.Position,
			Role:      &updated_user.Role.Name,
			Status:    &updated_user.Status,
			LoginDate: &updated_user.LoginDate,
		}
		return c.Status(fiber.StatusOK).JSON(oneUser)
	}

	oneUser := models_api.UserRespond{
		Picture:   updated_user.Picture,
		Password:  updated_user.Password,
		FirstName: updated_user.FirstName,
		LastName:  updated_user.LastName,
		Email:     updated_user.Email,
		Position:  updated_user.Position,
		Role:      &updated_user.Role.Name,
	}

	return c.Status(fiber.StatusOK).JSON(oneUser)
}

func DeleteUser(c *fiber.Ctx) error {
	slug := c.Params("slug")
	var user models.UserInfo

	// First, find the user by slug
	if err := db.DB.Where("slug = ?", slug).First(&user).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return c.Status(fiber.StatusNotFound).JSON(fiber.Map{
				"error": "User not found",
			})
		}
		return c.Status(fiber.StatusInternalServerError).JSON(fiber.Map{
			"error": "Failed to find user",
		})
	}

	// Delete the user
	if err := db.DB.Delete(&user).Error; err != nil {
		return c.Status(fiber.StatusInternalServerError).JSON(fiber.Map{
			"error": "Failed to delete user",
		})
	}

	return c.Status(fiber.StatusOK).JSON(fiber.Map{
		"message": "User deleted successfully",
	})
}
