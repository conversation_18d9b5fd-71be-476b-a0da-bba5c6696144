package controllers

import (
	"os"
	"time"

	"med-api/db"
	"med-api/models"
	models_api "med-api/models-api"
	"med-api/utils"

	"github.com/gofiber/fiber/v2"
	"github.com/golang-jwt/jwt/v5"
	"golang.org/x/crypto/bcrypt"
)

func Signup(c *fiber.Ctx) error {
	var input models_api.SignUpRequest
	// Hash the password
	// Hash the password before saving (optional, but recommended)
	if err := c.BodyParser(&input); err != nil {
		return c.Status(fiber.StatusBadRequest).JSON(fiber.Map{"error": err.Error()})
	}

	hashedPassword, err := utils.HashPassword(input.Password)
	if err != nil {
		return c.Status(fiber.StatusInternalServerError).JSON(fiber.Map{
			"error": "Failed to hash password",
		})
	}

	input.Password = hashedPassword

	user := models.UserInfo{
		Username:  input.Username,
		Password:  input.Password,
		FirstName: input.FirstName,
		LastName:  input.LastName,
		Email:     input.Email,
		// LoginDate: time.Now().Format("2006-01-02 15:04:05"),
		RoleID: 1,
		Status: "active",
		Slug:   utils.GenerateUUIDSlug(),
	}

	if err := db.DB.Where("username = ?", input.Username).First(&user).Error; err == nil {
		return c.Status(fiber.StatusBadRequest).JSON(fiber.Map{"error": "Username already exists"})
	}

	if err := db.DB.Create(&user).Error; err != nil {
		return c.Status(fiber.StatusInternalServerError).JSON(fiber.Map{
			"error": "Failed to create user",
		})
	}

	return c.Status(fiber.StatusCreated).JSON(fiber.Map{"message": "Signup successful"})
}

// Dummy login logic — replace with real user auth if needed
func Login(c *fiber.Ctx) error {
	var creds models_api.LoginRequest
	if err := c.BodyParser(&creds); err != nil {
		return c.Status(fiber.StatusUnauthorized).JSON(fiber.Map{"error": "Invalid credentials "})
	}

	var user models.UserInfo
	// Preload the "Role" relationship to ensure it's included
	if err := db.DB.Preload("Role").Where("username = ?", creds.Username).First(&user).Error; err != nil {
		return c.Status(fiber.StatusUnauthorized).JSON(fiber.Map{"error": "Invalid credentials"})
	}

	// Compare the provided password with the stored hashed password
	if err := bcrypt.CompareHashAndPassword([]byte(user.Password), []byte(creds.Password)); err != nil {
		return c.Status(fiber.StatusUnauthorized).JSON(fiber.Map{"error": "Invalid credentials"})
	}

	// Create JWT token with user and role information
	token := jwt.NewWithClaims(jwt.SigningMethodHS256, jwt.MapClaims{
		"sub":  user.ID,
		"name": user.Username,
		"role": user.Role.Name, // Access role name
		"exp":  time.Now().Add(24 * time.Hour).Unix(),
	})

	tokenString, err := token.SignedString([]byte(os.Getenv("JWT_SECRET")))
	if err != nil {
		return c.Status(fiber.StatusInternalServerError).JSON(fiber.Map{"error": "Could not generate token"})
	}

	if err := db.DB.Model(&user).Update("LoginDate", time.Now().Format("2006-01-02 15:04:05")).Error; err != nil {
		return c.Status(fiber.StatusNotExtended).JSON(fiber.Map{
			"error": "Invalid Date",
		})
	}

	// Return the token to the client
	return c.Status(fiber.StatusOK).JSON(fiber.Map{"token": "bearer " + tokenString})
}
