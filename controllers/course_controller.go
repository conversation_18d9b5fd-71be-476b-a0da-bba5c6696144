package controllers

import (
	"fmt"
	"med-api/db"
	"med-api/models"
	models_request "med-api/models-request"

	"github.com/gofiber/fiber/v2"
)

func CreateCourse(c *fiber.Ctx) error {
	var courseRequest models_request.RequestCourse
	var user models.UserInfo

	if err := c.<PERSON>(&courseRequest); err != nil {
		return c.Status(fiber.StatusBadRequest).JSON(fiber.Map{"error": err.Error()})
	}

	if err := db.DB.Where("slug = ?", courseRequest.UserSlug).First(&user).Error; err != nil {
		return c.Status(fiber.StatusBadRequest).JSON(fiber.Map{"error": "User ID not found."})
	}

	userID := user.ID

	fmt.Println(user.Slug)
	fmt.Println(userID)

	newCourse := models.Courses{
		CourseName:        courseRequest.CourseName,
		CoursePicture:     courseRequest.CoursePicture,
		CourseDescription: courseRequest.CourseDescription,
		CourseDifficulty:  courseRequest.CourseDifficulty,
		CourseInstruction: courseRequest.CourseInstruction,
		CourseDuration:    courseRequest.CourseDuration,
		CourseStatus:      courseRequest.CourseStatus,
		CourseCertificate: courseRequest.CourseCertificate,
		LecturerID:        userID,
	}

	if err := db.DB.Create(&newCourse).Error; err != nil {
		return c.Status(fiber.StatusBadRequest).JSON(fiber.Map{"error": err.Error()})
	}

	return c.Status(fiber.StatusOK).JSON("Create Success!")
}

func GetCourses(c *fiber.Ctx) error {
	return nil
}

func GetCourseID(c *fiber.Ctx) error {
	return nil
}

func UpdateCourse(c *fiber.Ctx) error {
	return nil
}

func DeleteCourse(c *fiber.Ctx) error {
	return nil
}
