package api_documents

import (
	"med-api/controllers"
	models_api "med-api/models-api"

	"github.com/canvas-tech-horizon/notelink"
)

func NoteRouteExamSignUp(api *notelink.ApiNote) {
	api.DocumentedRoute(notelink.DocumentedRouteInput{
		Method:      "POST",
		Path:        "/auth/signup",
		Description: "Create signup",
		Responses: map[string]string{
			"201": "Created",
			"400": "Bad Request",
			"500": "Internal Server Error",
		},
		Handler:         controllers.Signup,
		Params:          []notelink.Parameter{},
		SchemasRequest:  models_api.SignUpRequest{},
		SchemasResponse: nil,
	})
}

func NoteRouteExamLogin(api *notelink.ApiNote) {
	api.DocumentedRoute(notelink.DocumentedRouteInput{
		Method:      "POST",
		Path:        "/auth/login",
		Description: "Create login",
		Responses: map[string]string{
			"201": "Created",
			"400": "Bad Request",
			"500": "Internal Server Error",
		},
		Handler:         controllers.Login,
		Params:          []notelink.Parameter{},
		SchemasRequest:  models_api.LoginRequest{},
		SchemasResponse: nil,
	})
}
