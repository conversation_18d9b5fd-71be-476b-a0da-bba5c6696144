package models_request

type RequestCourse struct {
	CourseName        string `json:"course_name" binding:"required"`
	CoursePicture     string `json:"course_picture"`
	CourseDescription string `json:"course_description"`
	CourseInstruction string `json:"course_module"`
	CourseDifficulty  string `json:"course_difficulty"`
	CourseDuration    string `json:"course_duration"`
	CourseStatus      string `json:"course_status"`
	CourseCertificate bool   `json:"course_certificate"`
	UserSlug          uint   `json:"user_slug" binding:"required"`
}
