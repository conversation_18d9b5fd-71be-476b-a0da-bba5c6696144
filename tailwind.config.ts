import type { Config } from "tailwindcss";

export default {
  content: [
    "./src/pages/**/*.{js,ts,jsx,tsx,mdx}",
    "./src/components/**/*.{js,ts,jsx,tsx,mdx}",
    "./src/app/**/*.{js,ts,jsx,tsx,mdx}",
  ],
  theme: {
    extend: {
      colors: {
        background: "var(--background)",
        foreground: "var(--foreground)",
      },
      screens: { 
        'ipad-pro': { min: '1024px', max: '1199px'},
        "ipad-mini-landscape": { raw: "(min-width: 1024px) and (max-width: 1179px) and (orientation: landscape)" },
        "ipad-air-landscape": { raw: "(min-width: 1180px) and (max-width: 1366px) and (orientation: landscape)" },
        'ipad-pro-landscape': {'raw': '(min-width: 1366px) and (max-width: 1650px) and (orientation: landscape)'},
      },
    },
  },
  plugins: [
    require('daisyui')
  ],
} satisfies Config;
