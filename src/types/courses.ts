import { LearningStatus } from './progress';

export type ContentType = "video" | "pdf" | "text" | "quiz" | "final-exam"

// โครงสร้างข้อมูลเนื้อหา
export interface Content {
  id: string
  name: string
  typecontent: string
  details: string
  time: number
}

// โครงสร้างข้อมูลบทเรียน
export interface Lesson {
  id: string
  name: string
  description: string
  time: number
  content: Content[]
}

// โครงสร้างข้อมูลผู้สอน
export interface Teacher {
  name: string
  description: string
  avatar?: string
}

// โครงสร้างข้อมูลคอร์ส
export interface CourseType {
  title: any;
  flexibility: any;
  id: string
  name: string
  instruction: string
  description: string
  level: string
  time: number
  status: string
  certify: boolean
  coverImage?: string
  LearningStatus?: LearningStatus
  progress?: number
  teacher: Teacher
  lesson: Lesson[]
}
