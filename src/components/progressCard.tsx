import type React from "react"
import Image from "next/image"
import Link from "next/link"
import { Clock } from "lucide-react"

interface CourseCardProps {
  id: string
  name: string
  teacherName: string
  coverImage: string
  progress: number
  completedLessons: number
  totalLessons: number
  duration: number // ในหน่วยวินาที
  status: "completed" | "in_progress" | "not_started"
}

// ฟังก์ชันสำหรับกำหนดสีของแถบความก้าวหน้าตามเปอร์เซ็นต์
const getProgressColor = (progress: number): string => {
  if (progress < 30) return "bg-red-500" // น้อยกว่า 30% สีแดง
  if (progress < 70) return "bg-orange-500" // 30-70% สีส้ม
  return "bg-green-500" // มากกว่า 70% สีเขียว
}

// ฟังก์ชันแปลงสถานะการเรียนเป็นภาษาไทย
const translateStatus = (status: string): string => {
  switch (status) {
    case "completed":
      return "เรียนจบแล้ว"
    case "in_progress":
      return "กำลังเรียน"
    case "not_started":
      return "ยังไม่ได้เริ่ม"
    default:
      return status
  }
}

// ฟังก์ชันกำหนดสีของป้ายสถานะ
const getStatusBadgeColor = (status: string): string => {
  switch (status) {
    case "completed":
      return "bg-green-100 text-green-800"
    case "in_progress":
      return "bg-blue-100 text-blue-800"
    case "not_started":
      return "bg-gray-100 text-gray-800"
    default:
      return "bg-gray-100 text-gray-800"
  }
}

const CourseCard: React.FC<CourseCardProps> = ({
  id,
  name,
  teacherName,
  coverImage,
  progress,
  completedLessons,
  totalLessons,
  duration,
  status,
}) => {
  // คำนวณเวลาเรียนเป็นชั่วโมง
  const durationHours = Math.floor(duration / 3600)

  // กำหนดสีของแถบความก้าวหน้า
  const progressColor = getProgressColor(progress)

  // กำหนดสีของป้ายสถานะ
  const statusBadgeColor = getStatusBadgeColor(status)

  return (
    <div className="bg-white rounded-xl shadow-sm flex flex-col h-auto min-h-[350px] overflow-hidden">
      <div className="relative h-36 min-h-[9rem]">
        <Image
          src={
            coverImage ||
            `/placeholder.svg?height=144&width=384&query=medical+course+${encodeURIComponent(name) || "/placeholder.svg"}`
          }
          alt={name}
          layout="fill"
          objectFit="cover"
        />
        <div className="absolute top-2 right-2">
          <span className={`px-2 py-1 text-xs font-semibold rounded-full ${statusBadgeColor}`}>
            {translateStatus(status)}
          </span>
        </div>
      </div>

      <div className="p-4 flex flex-col flex-grow overflow-hidden">
        {/* ปรับปรุงการแสดงชื่อคอร์ส */}
        <h3 className="font-bold text-gray-800 mb-3 line-clamp-2 min-h-[2.8em] text-sm md:text-base leading-tight">
          {name}
        </h3>

        <div className="text-xs md:text-sm text-gray-600 mb-3 truncate">{teacherName}</div>

        <div className="mb-3">
          <div className="flex justify-between text-xs text-gray-500 mb-1">
            <span>ความก้าวหน้า</span>
            <span>{progress}%</span>
          </div>
          <div className="w-full h-2 bg-gray-200 rounded-full overflow-hidden">
            <div
              className={`h-full ${progressColor} rounded-full transition-all duration-300`}
              style={{ width: `${progress}%` }}
            ></div>
          </div>
        </div>

        <div className="text-xs text-gray-500 mb-4 flex items-center justify-between">
          <span className="whitespace-normal">
            {completedLessons} จาก {totalLessons} บทเรียน
          </span>
          <span className="flex items-center ml-1">
            <Clock size={12} className="mr-1 flex-shrink-0" />
            {durationHours} ชั่วโมง
          </span>
        </div>

        <div className="mt-auto">
          <Link
            href={`/courses/${id}`}
            className="block w-full text-center bg-[#008268] text-white py-2 rounded-md text-sm hover:bg-[#006e58] transition-colors"
          >
            {status === "completed" ? "ดูรายละเอียด" : "เรียนต่อ"}
          </Link>
        </div>
      </div>
    </div>
  )
}

export default CourseCard
