import { getCoursesData } from "@/data/allCourses";
import Link from "next/link";

const About = () => {
  return (
    <div className="relative overflow-hidden bg-[#D9E7E4] ">
      <div className="px-10 w-full h-full bg-white py-20">
        <h1 className="text-center text-2xl ibm-plex-sans-thai-semibold ">
          About
        </h1>
        <h1 className="text-center text-4xl lg:text-5xl ibm-plex-sans-thai-semibold text-transparent bg-clip-text bg-gradient-to-r from-[#35B6BD] to-[#008268] m-5">
          CareAcademy
        </h1>

        <p className="text-center text-2xl ibm-plex-sans-thai-medium mt-4">
          E-learning การแพทย์เพื่อสุขภาพ
          สำหรับผู้เรียนทุกระดับ{" "}
        </p>

        <div className="py-12 mt-8 ibm-plex-sans-thai-regular">
          <div className="max-w-7xl mx-auto grid grid-cols-1 sm:grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 px-4">
            {/* Card 1 */}
            <div className="rounded-lg shadow-lg overflow-hidden bg-[#2CBCA0] hover:shadow-xl transform hover:scale-110 transition-all duration-300">
              <div className="flex justify-center items-center h-48">
                <img
                  src="/e-med/img/notebook.png"
                  alt="Course Image"
                  className="h-48 object-cover"
                />
              </div>
              <div className="p-4">
                <h3 className="mt-2 text-lg font-semibold text-white">
                  เรียนรู้การวินิจฉัยและการรักษาโรคผ่าน E-learning
                </h3>
                <p className="text-white mt-2">
                  ใช้ E-learning
                  เพื่อฝึกฝนทักษะในการวินิจฉัยและรักษาผู้ป่วยโดยการเข้าถึงข้อมูลและเครื่องมือทางการแพทย์ที่ทันสมัยได้อย่างสะดวกและมีประสิทธิภาพ
                </p>
              </div>
            </div>

            {/* Card 2 */}
            <div className="rounded-lg shadow-lg overflow-hidden bg-[#2CBCA0] hover:shadow-xl transform hover:scale-110 transition-all duration-300">
              <div className="flex justify-center items-center h-48">
                <img
                  src="/e-med/img/graph.png"
                  alt="Course Image"
                  className="h-48 object-cover"
                />
              </div>
              <div className="p-4">
                <h3 className="mt-2 text-lg font-semibold text-white">
                  พัฒนาทักษะทางการแพทย์และสุขภาพผ่าน E-learning
                </h3>
                <p className="text-white mt-2">
                  เรียนรู้และพัฒนาทักษะการดูแลผู้ป่วยและการใช้เครื่องมือการแพทย์ผ่าน
                  E-learning ที่สะดวกและยืดหยุ่น
                </p>
              </div>
            </div>

            {/* Card 3 */}
            <div className="rounded-lg shadow-lg overflow-hidden bg-[#2CBCA0] hover:shadow-xl transform hover:scale-110 transition-all duration-300">
              <div className="flex justify-center items-center h-48">
                <img
                  src="/e-med/img/graph2.png"
                  alt="Course Image"
                  className="object-cover"
                />
              </div>
              <div className="p-4 ">
                <h3 className="mt-2 text-lg font-semibold text-white">
                  เตรียมความพร้อมในเทคโนโลยีด้านการแพทย์และสุขภาพ
                </h3>
                <p className="text-white mt-2">
                  ฝึกใช้เทคโนโลยีทางการแพทย์ เช่น AI และเครื่องมือทางการแพทย์ผ่าน
                  E-learning เพื่อเพิ่มประสิทธิภาพในการรักษาและวินิจฉัย
                </p>
              </div>
            </div>

            {/* Card 4 */}
            <div className="rounded-lg shadow-lg overflow-hidden bg-[#2CBCA0] hover:shadow-xl transform hover:scale-110 transition-all duration-300">
              <div className="flex justify-center items-center h-48">
                <img
                  src="/e-med/img/character.png"
                  alt="Course Image"
                  className="h-48 object-contain"
                />
              </div>
              <div className="p-4">
                <h3 className="mt-2 text-lg font-semibold text-white">
                  พัฒนาแนวทางการศึกษาด้านการแพทย์ให้สอดคล้องกับเทคโนโลยีใหม่
                </h3>
                <p className="text-white mt-2">
                  พัฒนาหลักสูตรและการศึกษาที่สอดคล้องกับการเปลี่ยนแปลงของเทคโนโลยีการแพทย์ผ่าน
                  E-learning ที่มีความยืดหยุ่น
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div className="px-10">
        <div className="flex justify-center items-center">
          <h1 className="w-64 text-center mt-20 text-4xl sm:text-5xl bg-gradient-to-r from-[#35B6BD] to-[#008268] bg-clip-text inline-block text-transparent ibm-plex-sans-thai-semibold ">
            คอร์สเรียน
          </h1>
        </div>

        <p className="text-center mt-6 text-xl ibm-plex-sans-thai-semibold">
          คอร์สเรียนแนะนำ(
          <span className="text-[#008266]">การแพทย์เพื่อสุขภาพ</span>) 3
          คอร์สเรียน
        </p>
        {/* 3 card */}
        <div className="ibm-plex-sans-thai-regular py-12">
          <div className="max-w-7xl mx-auto grid grid-cols-1 sm:grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 px-4">
            {getCoursesData.slice(0, 3).map((course) => (
              <Link key={course.id} href={`/courses/${course.id}`}>
                <div className="rounded-lg bg-white bg-opacity-80 shadow-xl hover:shadow-2xl transform hover:scale-105 transition-all duration-300">
                  {/* Colorful Card Background */}
                  <div className="relative overflow-hidden rounded-t-lg">
                    <img
                      src={course.coverImage}
                      alt={`Course ${course.id}`}
                      className="w-full h-48 object-cover"
                    />
                    <div className="absolute top-0 left-0 w-full h-full bg-gradient-to-b from-transparent to-blue-800 opacity-40" />
                  </div>
                  <div className="p-6 flex flex-col h-full">
                    {/* Course Type Badge */}
                    <div className="flex items-center justify-between mb-2">
                      <span className="text-sm text-[#164A7E] font-semibold flex items-center gap-2">
                        {/* Icon */}
                        <svg
                          aria-hidden="true"
                          focusable="false"
                          data-prefix="fas"
                          data-icon="certificate"
                          className="w-4 h-4 text-yellow-500"
                          xmlns="http://www.w3.org/2000/svg"
                          viewBox="0 0 512 512"
                        >
                          <path
                            fill="currentColor"
                            d="M211 7.3C205 1 196-1.4 187.6 .8s-14.9 8.9-17.1 17.3L154.7 80.6l-62-17.5c-8.4-2.4-17.4 0-23.5 6.1s-8.5 15.1-6.1 23.5l17.5 62L18.1 170.6c-8.4 2.1-15 8.7-17.3 17.1S1 205 7.3 211l46.2 45L7.3 301C1 307-1.4 316 .8 324.4s8.9 14.9 17.3 17.1l62.5 15.8-17.5 62c-2.4 8.4 0 17.4 6.1 23.5s15.1 8.5 23.5 6.1l62-17.5 15.8 62.5c2.1 8.4 8.7 15 17.1 17.3s17.3-.2 23.4-6.4l45-46.2 45 46.2c6.1 6.2 15 8.7 23.4 6.4s14.9-8.9 17.1-17.3l15.8-62.5 62 17.5c8.4 2.4 17.4 0 23.5-6.1s8.5-15.1 6.1-23.5l-17.5-62 62.5-15.8c8.4-2.1 15-8.7 17.3-17.1s-.2-17.3-6.4-23.4l-46.2-45 46.2-45c6.2-6.1 8.7-15 6.4-23.4s-8.9-14.9-17.3-17.1l-62.5-15.8 17.5-62c2.4-8.4 0-17.4-6.1-23.5s-15.1-8.5-23.5-6.1l-62 17.5L341.4 18.1c-2.1-8.4-8.7-15-17.1-17.3S307 1 301 7.3L256 53.5 211 7.3z"
                          ></path>
                        </svg>
                        {course.certify ? (
                          <span className="text-green-500 font-bold">
                            Certificate Available
                          </span>
                        ) : (
                          <span className="text-red-500 font-bold">
                            No Certificate
                          </span>
                        )}
                      </span>
                    </div>

                    {/* Course Name */}
                    <h3 className="mt-3 text-2xl font-bold text-gray-800 hover:text-blue-600 transition duration-200 truncate">
                      {course.name}
                    </h3>
                    {/* Course Description */}
                    <p className="text-sm text-gray-600 mt-2 line-clamp-3">
                      {course.description}
                    </p>
                    <div className="mt-6 flex items-center justify-between text-gray-500">
                      {/* Course Level */}
                      <span className="text-sm font-medium flex items-center gap-1">
                        <svg
                          xmlns="http://www.w3.org/2000/svg"
                          fill="currentColor"
                          viewBox="0 0 16 16"
                          className="w-4 h-4"
                        >
                          <path d="M8 1a7 7 0 100 14A7 7 0 008 1zM8 0a8 8 0 110 16A8 8 0 018 0z" />
                          <path d="M8 8.5L5.5 10l.5-2.5L4 5.5l2.5-.5L8 3l1.5 2 .5 2.5L11 7l-1.5 2L8 8.5z" />
                        </svg>
                        {course.level || "Beginner"}
                      </span>
                      {/* Number of Lessons */}
                      <span className="text-sm flex items-center gap-1">
                        <svg
                          xmlns="http://www.w3.org/2000/svg"
                          fill="currentColor"
                          viewBox="0 0 16 16"
                          className="w-4 h-4"
                        >
                          <path d="M8 1a7 7 0 100 14A7 7 0 008 1zM4.5 4h7v1h-7V4zm0 2h7v1h-7V6zm0 2h4v1h-4V8z" />
                        </svg>
                        {course.lesson.length} บทเรียน
                      </span>
                      {/* Course Duration */}
                      <span className="text-sm flex items-center gap-1">
                        <svg
                          xmlns="http://www.w3.org/2000/svg"
                          fill="currentColor"
                          viewBox="0 0 16 16"
                          className="w-4 h-4"
                        >
                          <path d="M8 1a7 7 0 100 14A7 7 0 008 1zM8 0a8 8 0 110 16A8 8 0 018 0z" />
                          <path d="M7.5 3h1v6h-1V3zm1 8v1h-1v-1h1z" />
                        </svg>
                        {Math.round(course.time / 3600)} ชม.
                      </span>
                    </div>
                  </div>
                </div>
              </Link>
            ))}
          </div>

          <div className="flex justify-center items-center mt-8">
            <a
              href="#"
              className="rounded-md border border-transparent bg-[#4F62A8] px-8 py-3 text-center font-medium text-white hover:bg-[##2CBCA0] ibm-plex-sans-thai-regular hover:shadow-xl transform hover:scale-105 transition-all duration-300"
            >
              ดูเพิ่มเติม
            </a>
          </div>
        </div>
      </div>

      {/* 4 card */}
    </div>
  );
};

export default About;