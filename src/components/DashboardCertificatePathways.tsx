"use client"

import React from 'react'
import Link from 'next/link'
import { 
  Award, 
  Clock,
  ChevronRight,
  Target,
  TrendingUp
} from 'lucide-react'

// Mock data types (simplified for dashboard)
interface DashboardCourse {
  id: string
  name: string
  isCompleted: boolean
  isUnlocked: boolean
}

interface DashboardPathway {
  id: string
  name: string
  description: string
  specialty: string
  difficulty: 'beginner' | 'intermediate' | 'advanced'
  estimatedDuration: number
  courses: DashboardCourse[]
  completedCourses: number
  totalCourses: number
  progressPercentage: number
  status: 'not_started' | 'in_progress' | 'completed'
}

// Featured pathways for dashboard (subset of full data)
const featuredPathways: DashboardPathway[] = [
  {
    id: "pneumonia-treatment",
    name: "การรักษาโรคปอดอักเสบ",
    description: "เรียนรู้การวินิจฉัยและรักษาโรคปอดอักเสบอย่างครอบคลุม",
    specialty: "respiratory",
    difficulty: "intermediate",
    estimatedDuration: 12,
    completedCourses: 2,
    totalCourses: 3,
    progressPercentage: 67,
    status: 'in_progress',
    courses: [
      { id: "WnH5F0qZF938", name: "โรคปอดอักเสบ: พื้นฐาน", isCompleted: true, isUnlocked: true },
      { id: "pneumonia-advanced", name: "การรักษาขั้นสูง", isCompleted: true, isUnlocked: true },
      { id: "pneumonia-icu", name: "การดูแลใน ICU", isCompleted: false, isUnlocked: true }
    ]
  },
  {
    id: "medication-management",
    name: "การจัดการยาผู้ป่วย",
    description: "ความรู้ครอบคลุมเกี่ยวกับการให้ยาและความปลอดภัยทางยา",
    specialty: "pharmacy",
    difficulty: "intermediate",
    estimatedDuration: 16,
    completedCourses: 1,
    totalCourses: 4,
    progressPercentage: 25,
    status: 'in_progress',
    courses: [
      { id: "QlH3I647MCuN", name: "หลักการให้ยา", isCompleted: true, isUnlocked: true },
      { id: "drug-calculation", name: "การคำนวณขนาดยา", isCompleted: false, isUnlocked: true },
      { id: "drug-interactions", name: "ปฏิกิริยาระหว่างยา", isCompleted: false, isUnlocked: false },
      { id: "medication-safety", name: "ความปลอดภัยทางยา", isCompleted: false, isUnlocked: false }
    ]
  },
  {
    id: "pediatric-obesity",
    name: "การรักษาโรคอ้วนในเด็ก",
    description: "แนวทางการรักษาและการจัดการโรคอ้วนในเด็กอย่างมีประสิทธิภาพ",
    specialty: "pediatrics",
    difficulty: "advanced",
    estimatedDuration: 10,
    completedCourses: 3,
    totalCourses: 3,
    progressPercentage: 100,
    status: 'completed',
    courses: [
      { id: "19x4uNv4RgED", name: "สาเหตุและการประเมิน", isCompleted: true, isUnlocked: true },
      { id: "pediatric-nutrition", name: "โภชนาการสำหรับเด็ก", isCompleted: true, isUnlocked: true },
      { id: "pediatric-exercise", name: "การออกกำลังกาย", isCompleted: true, isUnlocked: true }
    ]
  }
]

const DashboardCertificatePathways = () => {
  const getDifficultyColor = (difficulty: string) => {
    switch (difficulty) {
      case 'beginner': return 'bg-green-100 text-green-800'
      case 'intermediate': return 'bg-yellow-100 text-yellow-800'
      case 'advanced': return 'bg-red-100 text-red-800'
      default: return 'bg-gray-100 text-gray-800'
    }
  }

  const getDifficultyText = (difficulty: string) => {
    switch (difficulty) {
      case 'beginner': return 'เริ่มต้น'
      case 'intermediate': return 'ปานกลาง'
      case 'advanced': return 'ขั้นสูง'
      default: return difficulty
    }
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed': return 'text-green-600'
      case 'in_progress': return 'text-blue-600'
      case 'not_started': return 'text-gray-600'
      default: return 'text-gray-600'
    }
  }

  const getActionButton = (pathway: DashboardPathway) => {
    if (pathway.status === 'completed') {
      return (
        <Link
          href="/profile/certificates"
          className="inline-flex items-center px-3 py-1.5 bg-green-600 text-white text-sm rounded-md hover:bg-green-700 transition-colors"
        >
          <Award size={14} className="mr-1" />
          ดูใบประกาศนียบัตร
        </Link>
      )
    } else if (pathway.status === 'in_progress') {
      const nextCourse = pathway.courses.find(c => !c.isCompleted && c.isUnlocked)
      return (
        <Link
          href={nextCourse ? `/courses/${nextCourse.id}` : `/profile/certificate-pathways`}
          className="inline-flex items-center px-3 py-1.5 bg-[#008268] text-white text-sm rounded-md hover:bg-[#006e58] transition-colors"
        >
          <TrendingUp size={14} className="mr-1" />
          เรียนต่อ
        </Link>
      )
    } else {
      const firstCourse = pathway.courses[0]
      return (
        <Link
          href={firstCourse ? `/courses/${firstCourse.id}` : `/profile/certificate-pathways`}
          className="inline-flex items-center px-3 py-1.5 bg-[#008268] text-white text-sm rounded-md hover:bg-[#006e58] transition-colors"
        >
          <Target size={14} className="mr-1" />
          เริ่มเรียน
        </Link>
      )
    }
  }

  return (
    <div className="space-y-4">
      {featuredPathways.map((pathway) => (
        <div
          key={pathway.id}
          className="bg-white rounded-lg p-4 shadow-sm border hover:shadow-md transition-shadow duration-200"
        >
          {/* Header */}
          <div className="flex items-start justify-between mb-3">
            <div className="flex-1">
              <h3 className="font-semibold text-gray-900 text-sm mb-1">
                {pathway.name}
              </h3>
              <p className="text-xs text-gray-600 mb-2 line-clamp-2">
                {pathway.description}
              </p>
              <div className="flex items-center gap-2 mb-2">
                <span className={`text-xs px-2 py-0.5 rounded-full ${getDifficultyColor(pathway.difficulty)}`}>
                  {getDifficultyText(pathway.difficulty)}
                </span>
                <span className="text-xs text-gray-500 flex items-center">
                  <Clock size={10} className="mr-1" />
                  {pathway.estimatedDuration} ชม.
                </span>
              </div>
            </div>
            <div className="ml-3">
              <div className="w-10 h-10 bg-gradient-to-br from-[#008268] to-[#006e58] rounded-lg flex items-center justify-center">
                <Award size={20} className="text-white" />
              </div>
            </div>
          </div>

          {/* Progress */}
          <div className="mb-3">
            <div className="flex justify-between text-xs text-gray-600 mb-1">
              <span>ความก้าวหน้า</span>
              <span className={getStatusColor(pathway.status)}>
                {pathway.completedCourses} จาก {pathway.totalCourses} คอร์ส
              </span>
            </div>
            <div className="w-full h-1.5 bg-gray-200 rounded-full overflow-hidden">
              <div
                className={`h-full rounded-full transition-all duration-300 ${
                  pathway.status === 'completed' 
                    ? 'bg-green-500' 
                    : 'bg-[#008268]'
                }`}
                style={{ width: `${pathway.progressPercentage}%` }}
              ></div>
            </div>
            <div className="text-right text-xs text-gray-500 mt-1">
              {pathway.progressPercentage}%
            </div>
          </div>

          {/* Action */}
          <div className="flex items-center justify-between">
            {getActionButton(pathway)}
            <Link
              href="/profile/certificate-pathways"
              className="text-xs text-[#008268] hover:text-[#006e58] flex items-center"
            >
              รายละเอียด
              <ChevronRight size={12} className="ml-1" />
            </Link>
          </div>
        </div>
      ))}
    </div>
  )
}

export default DashboardCertificatePathways
