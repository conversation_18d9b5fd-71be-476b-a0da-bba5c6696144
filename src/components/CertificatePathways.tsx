"use client"

import React, { useEffect, useState } from 'react'
import Link from 'next/link'
import { useRouter } from 'next/navigation'
import { 
  Award, 
  BookOpen, 
  Calendar, 
  ArrowLeft,
  Clock,
  CheckCircle,
  Lock,
  Play,
  Filter,
  Search,
  ChevronRight,
  Target,
  TrendingUp
} from 'lucide-react'

// Mock data types
interface MockCourse {
  id: string
  name: string
  description: string
  coverImage: string
  duration: number // in hours
  difficulty: 'beginner' | 'intermediate' | 'advanced'
  isCompleted: boolean
  isUnlocked: boolean
}

interface CertificatePathway {
  id: string
  name: string
  description: string
  specialty: string
  difficulty: 'beginner' | 'intermediate' | 'advanced'
  estimatedDuration: number // total hours
  certificateImage: string
  courses: MockCourse[]
  prerequisites?: string[]
  completedCourses: number
  totalCourses: number
  progressPercentage: number
}

// Mock data for certificate pathways
const mockPathways: CertificatePathway[] = [
  {
    id: "pneumonia-treatment",
    name: "ใบประกาศนียบัตรการรักษาโรคปอดอักเสบ",
    description: "เรียนรู้การวินิจฉัยและรักษาโรคปอดอักเสบอย่างครอบคลุม ตั้งแต่พื้นฐานจนถึงการรักษาขั้นสูง",
    specialty: "respiratory",
    difficulty: "intermediate",
    estimatedDuration: 12,
    certificateImage: "/e-med/certificates/Certificate.pdf",
    completedCourses: 2,
    totalCourses: 3,
    progressPercentage: 67,
    courses: [
      {
        id: "WnH5F0qZF938",
        name: "โรคปอดอักเสบ: พื้นฐานและการวินิจฉัย",
        description: "เรียนรู้พื้นฐานของโรคปอดอักเสบและวิธีการวินิจฉัย",
        coverImage: "/e-med/img/courses05.jpg",
        duration: 4,
        difficulty: "beginner",
        isCompleted: true,
        isUnlocked: true
      },
      {
        id: "pneumonia-advanced",
        name: "การรักษาโรคปอดอักเสบขั้นสูง",
        description: "เทคนิคการรักษาขั้นสูงและการจัดการภาวะแทรกซ้อน",
        coverImage: "/e-med/img/courses06.jpg",
        duration: 5,
        difficulty: "intermediate",
        isCompleted: true,
        isUnlocked: true
      },
      {
        id: "pneumonia-icu",
        name: "การดูแลผู้ป่วยปอดอักเสบใน ICU",
        description: "การจัดการผู้ป่วยปอดอักเสบในหออภิบาลวิกฤต",
        coverImage: "/e-med/img/courses01.jpg",
        duration: 3,
        difficulty: "advanced",
        isCompleted: false,
        isUnlocked: true
      }
    ]
  },
  {
    id: "medication-management",
    name: "ใบประกาศนียบัตรการจัดการยาผู้ป่วย",
    description: "ความรู้ครอบคลุมเกี่ยวกับการให้ยา การคำนวณขนาดยา และความปลอดภัยทางยา",
    specialty: "pharmacy",
    difficulty: "intermediate",
    estimatedDuration: 16,
    certificateImage: "/e-med/certificates/Certificate.pdf",
    completedCourses: 1,
    totalCourses: 4,
    progressPercentage: 25,
    courses: [
      {
        id: "QlH3I647MCuN",
        name: "หลักการให้ยาแก่ผู้ป่วย",
        description: "พื้นฐานการให้ยาและหลักความปลอดภัย",
        coverImage: "/e-med/img/courses06.jpg",
        duration: 4,
        difficulty: "beginner",
        isCompleted: true,
        isUnlocked: true
      },
      {
        id: "drug-calculation",
        name: "การคำนวณขนาดยา",
        description: "เทคนิคการคำนวณขนาดยาที่ถูกต้องและปลอดภัย",
        coverImage: "/e-med/img/courses02.jpg",
        duration: 4,
        difficulty: "intermediate",
        isCompleted: false,
        isUnlocked: true
      },
      {
        id: "drug-interactions",
        name: "ปฏิกิริยาระหว่างยา",
        description: "การระบุและป้องกันปฏิกิริยาระหว่างยา",
        coverImage: "/e-med/img/courses01.jpg",
        duration: 4,
        difficulty: "intermediate",
        isCompleted: false,
        isUnlocked: false
      },
      {
        id: "medication-safety",
        name: "ความปลอดภัยทางยาขั้นสูง",
        description: "การจัดการความเสี่ยงและระบบความปลอดภัยทางยา",
        coverImage: "/e-med/img/courses05.jpg",
        duration: 4,
        difficulty: "advanced",
        isCompleted: false,
        isUnlocked: false
      }
    ]
  },
  {
    id: "pediatric-obesity",
    name: "ใบประกาศนียบัตรการรักษาโรคอ้วนในเด็ก",
    description: "แนวทางการรักษาและการจัดการโรคอ้วนในเด็กอย่างมีประสิทธิภาพ",
    specialty: "pediatrics",
    difficulty: "advanced",
    estimatedDuration: 10,
    certificateImage: "/e-med/certificates/Certificate.pdf",
    completedCourses: 3,
    totalCourses: 3,
    progressPercentage: 100,
    courses: [
      {
        id: "19x4uNv4RgED",
        name: "โรคอ้วนในเด็ก: สาเหตุและการประเมิน",
        description: "การประเมินและวินิจฉัยโรคอ้วนในเด็ก",
        coverImage: "/e-med/img/courses01.jpg",
        duration: 3,
        difficulty: "intermediate",
        isCompleted: true,
        isUnlocked: true
      },
      {
        id: "pediatric-nutrition",
        name: "โภชนาการสำหรับเด็กอ้วน",
        description: "การวางแผนโภชนาการและการปรับเปลี่ยนพฤติกรรม",
        coverImage: "/e-med/img/courses02.jpg",
        duration: 4,
        difficulty: "intermediate",
        isCompleted: true,
        isUnlocked: true
      },
      {
        id: "pediatric-exercise",
        name: "การออกกำลังกายสำหรับเด็กอ้วน",
        description: "โปรแกรมการออกกำลังกายที่เหมาะสมสำหรับเด็ก",
        coverImage: "/e-med/img/courses05.jpg",
        duration: 3,
        difficulty: "advanced",
        isCompleted: true,
        isUnlocked: true
      }
    ]
  },
  {
    id: "mental-health",
    name: "ใบประกาศนียบัตรการดูแลสุขภาพจิต",
    description: "ความรู้เกี่ยวกับการดูแลและรักษาผู้ป่วยโรคทางจิตเวช",
    specialty: "psychiatry",
    difficulty: "intermediate",
    estimatedDuration: 14,
    certificateImage: "/e-med/certificates/Certificate.pdf",
    completedCourses: 1,
    totalCourses: 3,
    progressPercentage: 33,
    courses: [
      {
        id: "j0DPSgeg4b2q",
        name: "การดูแลผู้ป่วยโรคซึมเศร้า",
        description: "การวินิจฉัยและรักษาโรคซึมเศร้า",
        coverImage: "/e-med/img/courses02.jpg",
        duration: 5,
        difficulty: "intermediate",
        isCompleted: true,
        isUnlocked: true
      },
      {
        id: "anxiety-disorders",
        name: "โรควิตกกังวลและการรักษา",
        description: "การจัดการโรควิตกกังวลในรูปแบบต่างๆ",
        coverImage: "/e-med/img/courses06.jpg",
        duration: 5,
        difficulty: "intermediate",
        isCompleted: false,
        isUnlocked: true
      },
      {
        id: "mental-health-emergency",
        name: "การจัดการภาวะฉุกเฉินทางจิตเวช",
        description: "การดูแลผู้ป่วยในภาวะวิกฤตทางจิตใจ",
        coverImage: "/e-med/img/courses01.jpg",
        duration: 4,
        difficulty: "advanced",
        isCompleted: false,
        isUnlocked: false
      }
    ]
  }
]

const CertificatePathways = () => {
  const router = useRouter()
  const [pathways, setPathways] = useState<CertificatePathway[]>([])
  const [filteredPathways, setFilteredPathways] = useState<CertificatePathway[]>([])
  const [loading, setLoading] = useState(true)
  const [searchTerm, setSearchTerm] = useState('')
  const [selectedSpecialty, setSelectedSpecialty] = useState<string>('all')
  const [selectedDifficulty, setSelectedDifficulty] = useState<string>('all')

  useEffect(() => {
    // Simulate loading delay
    const timer = setTimeout(() => {
      setPathways(mockPathways)
      setFilteredPathways(mockPathways)
      setLoading(false)
    }, 1000)

    return () => clearTimeout(timer)
  }, [])

  useEffect(() => {
    let filtered = pathways

    // Filter by search term
    if (searchTerm) {
      filtered = filtered.filter(pathway =>
        pathway.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        pathway.description.toLowerCase().includes(searchTerm.toLowerCase())
      )
    }

    // Filter by specialty
    if (selectedSpecialty !== 'all') {
      filtered = filtered.filter(pathway => pathway.specialty === selectedSpecialty)
    }

    // Filter by difficulty
    if (selectedDifficulty !== 'all') {
      filtered = filtered.filter(pathway => pathway.difficulty === selectedDifficulty)
    }

    setFilteredPathways(filtered)
  }, [pathways, searchTerm, selectedSpecialty, selectedDifficulty])

  const getDifficultyColor = (difficulty: string) => {
    switch (difficulty) {
      case 'beginner': return 'bg-green-100 text-green-800'
      case 'intermediate': return 'bg-yellow-100 text-yellow-800'
      case 'advanced': return 'bg-red-100 text-red-800'
      default: return 'bg-gray-100 text-gray-800'
    }
  }

  const getDifficultyText = (difficulty: string) => {
    switch (difficulty) {
      case 'beginner': return 'เริ่มต้น'
      case 'intermediate': return 'ปานกลาง'
      case 'advanced': return 'ขั้นสูง'
      default: return difficulty
    }
  }

  const getSpecialtyText = (specialty: string) => {
    switch (specialty) {
      case 'respiratory': return 'ระบบหายใจ'
      case 'pharmacy': return 'เภสัชกรรม'
      case 'pediatrics': return 'กุมารเวชศาสตร์'
      case 'psychiatry': return 'จิตเวชศาสตร์'
      default: return specialty
    }
  }

  if (loading) {
    return (
      <div className="min-h-screen bg-[#f9fafb] flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-[#008268] mx-auto mb-4"></div>
          <p className="text-gray-600">กำลังโหลดเส้นทางการเรียน...</p>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-[#f9fafb] w-full">
      <div className="flex w-full">
        {/* Sidebar */}
        <div className="w-[60px] fixed h-screen bg-[#008268] flex flex-col items-center py-6 z-30">
          <nav className="flex flex-col items-center gap-8 flex-grow">
            <Link
              href="/profile/dashboard"
              className="text-white p-3 rounded-xl hover:bg-[#006e58] transition-colors"
            >
              <BookOpen size={24} />
            </Link>
            <Link
              href="/profile/dashboard/my-courses"
              className="text-white p-3 rounded-xl hover:bg-[#006e58] transition-colors"
            >
              <BookOpen size={24} />
            </Link>
            <Link
              href="/profile/certificates"
              className="text-white p-3 rounded-xl hover:bg-[#006e58] transition-colors"
            >
              <Award size={24} />
            </Link>
            <Link href="/profile/certificate-pathways" className="text-white p-3 rounded-xl bg-[#006e58]">
              <Target size={24} />
            </Link>
            <Link href="/profile/schedule" className="text-white p-3 rounded-xl hover:bg-[#006e58] transition-colors">
              <Calendar size={24} />
            </Link>
          </nav>
        </div>

        {/* Main Content */}
        <div className="flex-1 ml-[60px] pt-[64px] w-[calc(100%-60px)]">
          <div className="w-full p-6">
            {/* Header */}
            <div className="mb-8">
              <div className="flex items-center mb-2">
                <button
                  onClick={() => router.push("/profile/dashboard")}
                  className="mr-2 p-1 rounded-full hover:bg-gray-200"
                >
                  <ArrowLeft size={20} />
                </button>
                <h1 className="text-2xl font-bold text-gray-800">เส้นทางการเรียนเพื่อรับใบประกาศนียบัตร</h1>
              </div>
              <p className="text-gray-600">
                เลือกเส้นทางการเรียนที่เหมาะสมกับคุณ เพื่อพัฒนาความรู้และรับใบประกาศนียบัตรในสาขาที่สนใจ
              </p>
            </div>

            {/* Search and Filters */}
            <div className="mb-8 bg-white rounded-lg p-6 shadow-sm border">
              <div className="flex flex-col lg:flex-row gap-4">
                {/* Search */}
                <div className="flex-1">
                  <div className="relative">
                    <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" size={20} />
                    <input
                      type="text"
                      placeholder="ค้นหาเส้นทางการเรียน..."
                      value={searchTerm}
                      onChange={(e) => setSearchTerm(e.target.value)}
                      className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-[#008268] focus:border-transparent"
                    />
                  </div>
                </div>

                {/* Specialty Filter */}
                <div className="lg:w-48">
                  <select
                    value={selectedSpecialty}
                    onChange={(e) => setSelectedSpecialty(e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-[#008268] focus:border-transparent"
                  >
                    <option value="all">ทุกสาขา</option>
                    <option value="respiratory">ระบบหายใจ</option>
                    <option value="pharmacy">เภสัชกรรม</option>
                    <option value="pediatrics">กุมารเวชศาสตร์</option>
                    <option value="psychiatry">จิตเวชศาสตร์</option>
                  </select>
                </div>

                {/* Difficulty Filter */}
                <div className="lg:w-48">
                  <select
                    value={selectedDifficulty}
                    onChange={(e) => setSelectedDifficulty(e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-[#008268] focus:border-transparent"
                  >
                    <option value="all">ทุกระดับ</option>
                    <option value="beginner">เริ่มต้น</option>
                    <option value="intermediate">ปานกลาง</option>
                    <option value="advanced">ขั้นสูง</option>
                  </select>
                </div>
              </div>
            </div>

            {/* Statistics */}
            <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
              <div className="bg-white rounded-lg p-6 shadow-sm border">
                <div className="flex items-center">
                  <div className="p-3 bg-[#008268]/10 rounded-lg">
                    <Target className="h-6 w-6 text-[#008268]" />
                  </div>
                  <div className="ml-4">
                    <p className="text-sm font-medium text-gray-600">เส้นทางทั้งหมด</p>
                    <p className="text-2xl font-bold text-gray-900">{pathways.length}</p>
                  </div>
                </div>
              </div>
              <div className="bg-white rounded-lg p-6 shadow-sm border">
                <div className="flex items-center">
                  <div className="p-3 bg-green-100 rounded-lg">
                    <CheckCircle className="h-6 w-6 text-green-600" />
                  </div>
                  <div className="ml-4">
                    <p className="text-sm font-medium text-gray-600">เส้นทางที่เรียนจบ</p>
                    <p className="text-2xl font-bold text-gray-900">
                      {pathways.filter(p => p.progressPercentage === 100).length}
                    </p>
                  </div>
                </div>
              </div>
              <div className="bg-white rounded-lg p-6 shadow-sm border">
                <div className="flex items-center">
                  <div className="p-3 bg-blue-100 rounded-lg">
                    <TrendingUp className="h-6 w-6 text-blue-600" />
                  </div>
                  <div className="ml-4">
                    <p className="text-sm font-medium text-gray-600">กำลังเรียน</p>
                    <p className="text-2xl font-bold text-gray-900">
                      {pathways.filter(p => p.progressPercentage > 0 && p.progressPercentage < 100).length}
                    </p>
                  </div>
                </div>
              </div>
              <div className="bg-white rounded-lg p-6 shadow-sm border">
                <div className="flex items-center">
                  <div className="p-3 bg-orange-100 rounded-lg">
                    <Clock className="h-6 w-6 text-orange-600" />
                  </div>
                  <div className="ml-4">
                    <p className="text-sm font-medium text-gray-600">เวลาเฉลี่ย</p>
                    <p className="text-2xl font-bold text-gray-900">
                      {Math.round(pathways.reduce((acc, p) => acc + p.estimatedDuration, 0) / pathways.length)} ชม.
                    </p>
                  </div>
                </div>
              </div>
            </div>

            {/* Pathway Cards */}
            {filteredPathways.length === 0 ? (
              <div className="text-center py-16">
                <div className="mb-6">
                  <Target size={64} className="mx-auto text-gray-300" />
                </div>
                <h3 className="text-xl font-semibold text-gray-600 mb-2">
                  ไม่พบเส้นทางการเรียนที่ตรงกับการค้นหา
                </h3>
                <p className="text-gray-500 mb-6">
                  ลองปรับเปลี่ยนคำค้นหาหรือตัวกรองเพื่อดูเส้นทางการเรียนอื่นๆ
                </p>
                <button
                  onClick={() => {
                    setSearchTerm('')
                    setSelectedSpecialty('all')
                    setSelectedDifficulty('all')
                  }}
                  className="px-6 py-3 bg-[#008268] text-white rounded-lg hover:bg-[#006e58] transition-colors"
                >
                  ล้างตัวกรอง
                </button>
              </div>
            ) : (
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
                {filteredPathways.map((pathway) => (
                  <div
                    key={pathway.id}
                    className="bg-white rounded-lg shadow-sm border hover:shadow-md transition-shadow duration-200"
                  >
                    {/* Pathway Header */}
                    <div className="p-6 border-b border-gray-100">
                      <div className="flex items-start justify-between mb-4">
                        <div className="flex-1">
                          <h3 className="text-xl font-semibold text-gray-900 mb-2">
                            {pathway.name}
                          </h3>
                          <p className="text-gray-600 text-sm mb-3">
                            {pathway.description}
                          </p>
                          <div className="flex items-center gap-3 mb-4">
                            <span className="text-xs px-2 py-1 bg-blue-100 text-blue-800 rounded-full">
                              {getSpecialtyText(pathway.specialty)}
                            </span>
                            <span className={`text-xs px-2 py-1 rounded-full ${getDifficultyColor(pathway.difficulty)}`}>
                              {getDifficultyText(pathway.difficulty)}
                            </span>
                            <span className="text-xs text-gray-500 flex items-center">
                              <Clock size={12} className="mr-1" />
                              {pathway.estimatedDuration} ชั่วโมง
                            </span>
                          </div>
                        </div>
                        <div className="ml-4">
                          <div className="w-16 h-16 bg-gradient-to-br from-[#008268] to-[#006e58] rounded-lg flex items-center justify-center">
                            <Award size={32} className="text-white" />
                          </div>
                        </div>
                      </div>

                      {/* Progress */}
                      <div className="mb-4">
                        <div className="flex justify-between text-sm text-gray-600 mb-2">
                          <span>ความก้าวหน้า</span>
                          <span>{pathway.completedCourses} จาก {pathway.totalCourses} คอร์ส</span>
                        </div>
                        <div className="w-full h-2 bg-gray-200 rounded-full overflow-hidden">
                          <div
                            className="h-full bg-[#008268] rounded-full transition-all duration-300"
                            style={{ width: `${pathway.progressPercentage}%` }}
                          ></div>
                        </div>
                        <div className="text-right text-sm text-gray-500 mt-1">
                          {pathway.progressPercentage}%
                        </div>
                      </div>
                    </div>

                    {/* Course Sequence */}
                    <div className="p-6">
                      <h4 className="font-semibold text-gray-900 mb-4">ลำดับการเรียน</h4>
                      <div className="space-y-3">
                        {pathway.courses.map((course, index) => (
                          <div key={course.id} className="flex items-center">
                            {/* Step Number */}
                            <div className={`w-8 h-8 rounded-full flex items-center justify-center text-sm font-semibold mr-3 ${
                              course.isCompleted
                                ? 'bg-green-100 text-green-800'
                                : course.isUnlocked
                                  ? 'bg-[#008268] text-white'
                                  : 'bg-gray-100 text-gray-400'
                            }`}>
                              {course.isCompleted ? (
                                <CheckCircle size={16} />
                              ) : course.isUnlocked ? (
                                index + 1
                              ) : (
                                <Lock size={16} />
                              )}
                            </div>

                            {/* Course Info */}
                            <div className="flex-1">
                              <div className={`font-medium ${
                                course.isUnlocked ? 'text-gray-900' : 'text-gray-400'
                              }`}>
                                {course.name}
                              </div>
                              <div className="text-sm text-gray-500 flex items-center">
                                <Clock size={12} className="mr-1" />
                                {course.duration} ชั่วโมง
                                <span className="mx-2">•</span>
                                {getDifficultyText(course.difficulty)}
                              </div>
                            </div>

                            {/* Action Button */}
                            <div className="ml-3">
                              {course.isCompleted ? (
                                <Link
                                  href={`/courses/${course.id}`}
                                  className="text-green-600 hover:text-green-700 text-sm font-medium"
                                >
                                  ดูรายละเอียด
                                </Link>
                              ) : course.isUnlocked ? (
                                <Link
                                  href={`/courses/${course.id}`}
                                  className="text-[#008268] hover:text-[#006e58] text-sm font-medium flex items-center"
                                >
                                  เริ่มเรียน
                                  <ChevronRight size={16} className="ml-1" />
                                </Link>
                              ) : (
                                <span className="text-gray-400 text-sm">ล็อค</span>
                              )}
                            </div>

                            {/* Connecting Line */}
                            {index < pathway.courses.length - 1 && (
                              <div className="absolute left-[52px] mt-8 w-0.5 h-6 bg-gray-200"></div>
                            )}
                          </div>
                        ))}
                      </div>

                      {/* Action Buttons */}
                      <div className="mt-6 pt-4 border-t border-gray-100">
                        <div className="flex gap-3">
                          {pathway.progressPercentage === 100 ? (
                            <Link
                              href="/profile/certificates"
                              className="flex-1 flex items-center justify-center px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 transition-colors"
                            >
                              <Award size={16} className="mr-2" />
                              ดูใบประกาศนียบัตร
                            </Link>
                          ) : pathway.progressPercentage > 0 ? (
                            <button
                              onClick={() => {
                                const nextCourse = pathway.courses.find(c => !c.isCompleted && c.isUnlocked)
                                if (nextCourse) {
                                  router.push(`/courses/${nextCourse.id}`)
                                }
                              }}
                              className="flex-1 flex items-center justify-center px-4 py-2 bg-[#008268] text-white rounded-md hover:bg-[#006e58] transition-colors"
                            >
                              <Play size={16} className="mr-2" />
                              เรียนต่อ
                            </button>
                          ) : (
                            <button
                              onClick={() => {
                                const firstCourse = pathway.courses[0]
                                if (firstCourse) {
                                  router.push(`/courses/${firstCourse.id}`)
                                }
                              }}
                              className="flex-1 flex items-center justify-center px-4 py-2 bg-[#008268] text-white rounded-md hover:bg-[#006e58] transition-colors"
                            >
                              <Play size={16} className="mr-2" />
                              เริ่มเส้นทางการเรียน
                            </button>
                          )}
                          <button className="px-4 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50 transition-colors">
                            รายละเอียด
                          </button>
                        </div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  )
}

export default CertificatePathways
