"use client"

import type React from "react"
import { useState } from "react"
import Image from "next/image"
import Link from "next/link"
import { useRouter } from "next/navigation"
import { LockKeyhole } from "lucide-react"

export default function ForgotPasswordPage() {
  const [email, setEmail] = useState("")
  const [error, setError] = useState("")
  const router = useRouter()

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()

    // Basic validation
    if (!email) {
      setError("กรุณากรอกอีเมล")
      return
    }

    // Here you would typically call an API to send a reset email
    console.log("Sending reset email to:", email)

    // For demo purposes, we'll just navigate to the OTP verification page
    router.push(`/verify-otp?email=${encodeURIComponent(email)}`)
  }

  return (
    <div className="min-h-screen bg-[#D0E2DF] flex justify-center items-center">
      {/* Main Content */}
      <div className="w-full max-w-4xl">
        <div className="bg-white rounded-2xl lg:w-[100vh] lg:h-[60h] overflow-hidden shadow-xl flex flex-col md:flex-row">
          {/* Left Side - Background Image */}
          <div className="relative hidden md:block w-1/2">
            <div className="absolute inset-0 bg-[#3B4A8F] opacity-75 z-10" />
            <Image
              src="/e-med/img/hospital_bg.png"
              alt="Hospital Background"
              layout="fill"
              objectFit="cover"
              className="object-cover z-0"
              priority
            />
            <div className="absolute bottom-0 left-0 p-4 z-20">
              <h1 className="text-white p-5 text-5xl font-extrabold leading-tight shadow-foreground">
                E-MED
                <br />
                LEARNING
              </h1>
            </div>
          </div>

          {/* Right Side - Forgot Password Form */}
          <div className="w-full md:w-1/2 p-8 flex flex-col justify-center">
            <h2 className="text-3xl font-bold text-center mt-10 mb-2">รีเซ็ทหรือผ่��น</h2>

            <div className="flex justify-center mb-6">
              <div className="bg-blue-100 p-6 rounded-full">
                <LockKeyhole size={80} className="text-[#3B4A8F]" />
              </div>
            </div>

            <form onSubmit={handleSubmit} className="space-y-6">
              <div>
                <label htmlFor="email" className="block text-sm font-medium text-gray-700 mb-1">
                  อีเมล
                </label>
                <input
                  id="email"
                  name="email"
                  type="email"
                  required
                  value={email}
                  onChange={(e) => setEmail(e.target.value)}
                  className="w-full px-3 py-2 bg-white border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-[#2FBCC1]"
                />
                {error && <p className="text-red-500 text-xs mt-1">{error}</p>}
              </div>

              <button
                type="submit"
                className="w-full bg-[#3B4A8F] hover:bg-[#3B4A8F]/90 text-white font-bold text-lg py-2 px-4 rounded-md"
              >
                ส่งอีเมลยืนยัน
              </button>
            </form>

            <div className="text-center mt-4">
              <Link href="/login" className="font-medium text-[#3B4A8F] hover:text-[#3B4A8F]/80">
                กลับไปหน้าเข้าสู่ระบบ
              </Link>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
