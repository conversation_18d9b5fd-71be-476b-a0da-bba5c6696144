"use client"

import type React from "react"
import { useState } from "react"
import Image from "next/image"
import Navbar from "@/components/headers"
import { useRouter, useSearchParams } from "next/navigation"

export default function ResetPasswordPage() {
    const searchParams = useSearchParams()
    const email = searchParams.get("email") || ""

    const [formData, setFormData] = useState({
        password: "",
        confirmPassword: "",
    })

    const [errors, setErrors] = useState({
        password: "",
        confirmPassword: "",
    })

    const router = useRouter()

    const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        const { name, value } = e.target
        setFormData((prev) => ({
            ...prev,
            [name]: value,
        }))
    }

    const handleSubmit = (e: React.FormEvent) => {
        e.preventDefault()

        // Basic validation
        const newErrors: any = {}
        if (!formData.password) newErrors.password = "กรุณากรอกรหัสผ่าน"
        if (formData.password.length < 8) newErrors.password = "รหัสผ่านต้องมีอย่างน้อย 8 ตัวอักษร"
        if (!formData.confirmPassword) newErrors.confirmPassword = "กรุณายืนยันรหัสผ่าน"
        if (formData.password !== formData.confirmPassword) newErrors.confirmPassword = "รหัสผ่านไม่ตรงกัน"

        setErrors(newErrors)

        // If no errors, proceed with password reset
        if (Object.keys(newErrors).length === 0) {
            console.log("Resetting password for:", email)
            console.log("New password:", formData.password)

            // Here you would typically call an API to reset the password

            // For demo purposes, we'll just navigate to the login page
            router.push("/login")
        }
    }

    return (
        <><Navbar />
            <div className="min-h-screen bg-[#D0E2DF] flex justify-center items-center">
                {/* Main Content */}
                <div className="w-full max-w-4xl">
                    <div className="bg-white rounded-2xl lg:w-[100vh] lg:h-[60vh] overflow-hidden shadow-xl flex flex-col md:flex-row">
                        {/* Left Side - Background Image */}
                        <div className="relative hidden md:block w-1/2">
                            <div className="absolute inset-0 bg-[#3B4A8F] opacity-75 z-10" />
                            <Image
                                src="/e-med/img/hospital_bg.png"
                                alt="Hospital Background"
                                layout="fill"
                                objectFit="cover"
                                className="object-cover z-0"
                                priority />
                            <div className="absolute bottom-0 left-0 p-4 z-20">
                                <h1 className="text-white p-5 text-5xl font-extrabold leading-tight shadow-foreground">
                                    E-MED
                                    <br />
                                    LEARNING
                                </h1>
                            </div>
                        </div>

                        {/* Right Side - Reset Password Form */}
                        <div className="w-full md:w-1/2 p-8 flex flex-col justify-between h-full">
                            <div>
                                <h2 className="text-3xl font-bold text-center mt-10 mb-2">รีเซ็ทรหัสผ่าน</h2>
                                <p className="text-center text-sm text-gray-600 mb-6">กรอกรหัสผ่านใหม่เพื่อการเปลี่ยนรหัสผ่านของคุณ</p>

                                <form onSubmit={handleSubmit} className="space-y-6">
                                    <div>
                                        <label htmlFor="password" className="block text-sm font-medium text-gray-700 mb-1">
                                            รหัสผ่าน
                                        </label>
                                        <input
                                            id="password"
                                            name="password"
                                            type="password"
                                            required
                                            value={formData.password}
                                            onChange={handleChange}
                                            className="w-full px-3 py-2 bg-white border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-[#2FBCC1]" />
                                        {errors.password && <p className="text-red-500 text-xs mt-1">{errors.password}</p>}
                                    </div>

                                    <div>
                                        <label htmlFor="confirmPassword" className="block text-sm font-medium text-gray-700 mb-1">
                                            ยืนยันรหัสผ่าน
                                        </label>
                                        <input
                                            id="confirmPassword"
                                            name="confirmPassword"
                                            type="password"
                                            required
                                            value={formData.confirmPassword}
                                            onChange={handleChange}
                                            className="w-full px-3 py-2 bg-white border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-[#2FBCC1]" />
                                        {errors.confirmPassword && <p className="text-red-500 text-xs mt-1">{errors.confirmPassword}</p>}
                                    </div>
                                </form>
                            </div>

                            <button
                                type="submit"
                                className="w-full bg-[#3B4A8F] hover:bg-[#3B4A8F]/90 text-white font-bold text-lg py-2 px-4 rounded-md mt-auto"
                                onClick={handleSubmit}
                            >
                                รีเซ็ทรหัสผ่าน
                            </button>
                        </div>
                    </div>
                </div>
            </div></>
    )
}
