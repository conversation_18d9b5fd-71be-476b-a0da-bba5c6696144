"use client"

import type React from "react"
import { useState } from "react"
import Image from "next/image"
import Link from "next/link"
import G<PERSON>Hub<PERSON>eatmap from "@/components/table"
import Navbar from "@/components/headers"


export default function RegisterPage() {
  const [formData, setFormData] = useState({
    name: "",
    email: "",
    password: "",
    confirmPassword: "",
  })

  const [errors, setErrors] = useState({
    name: "",
    email: "",
    password: "",
    confirmPassword: "",
  })

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()

    // Basic validation
    const newErrors: any = {}
    if (!formData.name) newErrors.name = "กรุณากรอกชื่อ - สกุล"
    if (!formData.email) newErrors.email = "กรุณากรอกอีเมล"
    if (!formData.password) newErrors.password = "กรุณากรอกพาสเวิร์ด"
    if (formData.password !== formData.confirmPassword)
      newErrors.confirmPassword = "พาสเวิร์ดไม่ตรงกัน"

    setErrors(newErrors)

    // If no errors, proceed with registration logic
    if (Object.keys(newErrors).length === 0) {
      console.log(formData)
      // Add your registration logic here
    }
  }

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target
    setFormData((prev) => ({
      ...prev,
      [name]: value,
    }))
  }

  return (
    <><Navbar /><div className="min-h-screen bg-[#D0E2DF] flex justify-center items-center">
      {/* Main Content */}
      <div className="w-full max-w-4xl">
        <div className="bg-white rounded-2xl lg:w-[100vh] lg:h-[60h] overflow-hidden shadow-xl flex flex-col md:flex-row">
          {/* Left Side - Background Image */}
          <div className="relative hidden md:block w-1/2">
            <div className="absolute inset-0 bg-[#004C41] opacity-75 z-10" />
            <Image
              src="/e-med/img/hospital_bg.png"
              alt="Hospital Background"
              layout="fill"
              objectFit="cover"
              className="object-cover z-0"
              priority />
            <div className="absolute bottom-0 left-0 p-4 z-20">
              <h1 className="text-white p-5 text-5xl font-extrabold leading-tight shadow-foreground">
                E-MED
                <br />
                LEARNING
              </h1>
            </div>
          </div>

          {/* Right Side - Registration Form */}
          <div className="w-full md:w-1/2 p-8 flex flex-col justify-center">
            <h2 className="text-3xl font-bold text-center mt-10 mb-5">สมัครสมาชิก</h2>
            <form onSubmit={handleSubmit} className="space-y-6">
              <div>
                <label htmlFor="name" className="block text-sm font-medium text-gray-700 mb-1">
                  ชื่อ - สกุล
                </label>
                <input
                  id="name"
                  name="name"
                  type="text"
                  required
                  value={formData.name}
                  onChange={handleChange}
                  className="w-full px-3 py-2 bg-white border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-[#2FBCC1]" />
                {errors.name && <p className="text-red-500 text-xs mt-1">{errors.name}</p>}
              </div>

              <div>
                <label htmlFor="email" className="block text-sm font-medium text-gray-700 mb-1">
                  อีเมล
                </label>
                <input
                  id="email"
                  name="email"
                  type="email"
                  required
                  value={formData.email}
                  onChange={handleChange}
                  className="w-full px-3 py-2 bg-white border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-[#2FBCC1]" />
                {errors.email && <p className="text-red-500 text-xs mt-1">{errors.email}</p>}
              </div>

              <div>
                <label htmlFor="password" className="block text-sm font-medium text-gray-700 mb-1">
                  พาสเวิร์ด
                </label>
                <input
                  id="password"
                  name="password"
                  type="password"
                  required
                  value={formData.password}
                  onChange={handleChange}
                  className="w-full px-3 py-2 bg-white border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-[#2FBCC1]" />
                {errors.password && <p className="text-red-500 text-xs mt-1">{errors.password}</p>}
              </div>

              <div>
                <label htmlFor="confirmPassword" className="block text-sm font-medium text-gray-700 mb-1">
                  ยืนยันพาสเวิร์ด
                </label>
                <input
                  id="confirmPassword"
                  name="confirmPassword"
                  type="password"
                  required
                  value={formData.confirmPassword}
                  onChange={handleChange}
                  className="w-full px-3 py-2 bg-white border border-gray-300 rounded-md focus:outline-none focus:ring-2 mb-2 focus:ring-[#2FBCC1]" />
                {errors.confirmPassword && <p className="text-red-500 text-xs mt-1">{errors.confirmPassword}</p>}
              </div>

              <button
                type="submit"
                className="w-full bg-[#008268] hover:bg-[#6aafa1]/90 text-white font-bold text-lg py-2 px-4 rounded-md"
              >
                สมัครสมาชิก
              </button>
            </form>
          </div>
        </div>
      </div>
    </div></>
  )
}
